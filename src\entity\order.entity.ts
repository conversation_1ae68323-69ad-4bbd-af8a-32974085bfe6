import {
  Table,
  Column,
  Model,
  DataType,
  BelongsTo,
  HasMany,
  ForeignKey,
  // BeforeCreate,
} from 'sequelize-typescript';
import { OrderDiscountInfo } from './order-discount-info.entity';
import { Customer } from './customer.entity';
import { Employee } from './employee.entity';
import { OrderDetail } from './order-detail.entity';
import { ServiceChangeLog } from './service-change-log.entity';
import { Review } from './review.entity';
import { Complaint } from './complaint.entity';
import { CustomerAddress } from './address.entity';
import { ServicePhoto } from './service-photo.entity';
import { OrderSpecialNote } from './order-special-note.entity';
import { OrderStatus } from '../common/Constant';

export interface OrderAttributes {
  /** 订单ID */
  id: number;
  /** 订单编号 */
  sn: string;
  /** 客户ID */
  customerId: number;
  /** 员工ID */
  employeeId?: number;
  /** 订单状态 */
  status: string;
  /** 虚拟字段，是否过期 */
  isExpired: boolean;
  /** 下单时间 */
  orderTime: Date;
  /** 预约服务时间 */
  serviceTime?: Date;
  /** 地址ID */
  addressId?: number;
  /** 服务地址 */
  address: string;
  /** 服务地址经度 */
  longitude: number;
  /** 服务地址纬度 */
  latitude: number;
  /** 服务地址详情 */
  addressDetail: string;
  /** 服务地址备注 */
  addressRemark?: string;
  /** 订单原价 */
  originalPrice: number;
  /** 订单总费用 */
  totalFee: number;
  /** 权益卡抵扣金额 */
  cardDeduction: number;
  /** 代金券抵扣金额 */
  couponDeduction: number;
  /** 是否有追加服务 */
  hasAdditionalServices: boolean;
  /** 追加服务实付总价（所有状态的追加服务totalFee总和） */
  additionalServiceAmount: number;
  /** 追加服务原总价 */
  additionalServiceOriginalPrice: number;
  /** 追加服务是否全部完成 */
  additionalServicesCompleted: boolean;
  /** 是否有特殊情况说明 */
  hasSpecialNote: boolean;
  /** 微信支付预支付ID */
  prepay_id?: string;
  /** 关联客户信息 */
  customer?: Customer;
  /** 关联员工信息 */
  employee?: Employee;
  /** 关联地址信息 */
  customerAddress?: CustomerAddress;
  /** 订单明细列表 */
  orderDetails?: OrderDetail[];
  /** 服务变更记录列表 */
  changeLogs?: ServiceChangeLog[];
  /** 评价信息 */
  reviews?: Review[];
  /** 投诉记录列表 */
  complaints?: Complaint[];
  /** 优惠信息列表 */
  discountInfos?: OrderDiscountInfo[];
  /** 服务照片列表 */
  servicePhotos?: ServicePhoto[];
  /** 特殊情况说明列表 */
  specialNotes?: OrderSpecialNote[];
  createdAt?: Date;
  updatedAt?: Date;
}

@Table({ tableName: 'orders', timestamps: true, comment: '订单表' })
export class Order extends Model<OrderAttributes> implements OrderAttributes {
  @Column({
    type: DataType.INTEGER,
    primaryKey: true,
    autoIncrement: true,
    comment: '订单ID',
  })
  id: number;

  @Column({
    type: DataType.STRING(20),
    allowNull: false,
    comment: '订单编号',
  })
  sn: string;

  @Column({
    type: DataType.INTEGER,
    allowNull: false,
    comment: '客户ID',
  })
  @ForeignKey(() => Customer)
  customerId: number;

  @Column({
    type: DataType.INTEGER,
    comment: '员工ID',
  })
  @ForeignKey(() => Employee)
  employeeId: number;

  @Column({
    type: DataType.STRING(20),
    defaultValue: OrderStatus.待付款,
    comment: '订单状态',
  })
  status: string;

  @Column({
    type: DataType.VIRTUAL,
    get() {
      const status = this.getDataValue('status');
      const createdAt = this.getDataValue('createdAt');
      const now = new Date().getTime();
      // 待付款且创建时间超过15分钟，视为过期
      return status === OrderStatus.待付款 && createdAt < now - 15 * 60 * 1000;
    },
    comment: '是否过期',
  })
  isExpired: boolean;

  @Column({
    type: DataType.DATE,
    allowNull: false,
    comment: '下单时间',
  })
  orderTime: Date;

  @Column({
    type: DataType.DATE,
    comment: '预约服务时间',
  })
  serviceTime: Date;

  /** 地址ID */
  @Column({
    type: DataType.INTEGER,
    comment: '地址ID',
  })
  @ForeignKey(() => CustomerAddress)
  addressId?: number;

  /** 服务地址 */
  @Column({
    type: DataType.STRING(255),
    comment: '服务地址',
  })
  address: string;

  /** 服务地址经度 */
  @Column({
    type: DataType.DECIMAL(10, 6),
    comment: '服务地址经度',
  })
  longitude: number;

  /** 服务地址纬度 */
  @Column({
    type: DataType.DECIMAL(10, 6),
    comment: '服务地址纬度',
  })
  latitude: number;

  /** 服务地址详情 */
  @Column({
    type: DataType.STRING(255),
    comment: '服务地址详情',
  })
  addressDetail: string;

  /** 服务地址备注 */
  @Column({
    type: DataType.STRING(255),
    comment: '服务地址备注',
  })
  addressRemark?: string;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '订单原价',
  })
  originalPrice: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    allowNull: false,
    comment: '订单总费用',
  })
  totalFee: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    defaultValue: 0.0,
    comment: '权益卡抵扣金额',
  })
  cardDeduction: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    defaultValue: 0.0,
    comment: '代金券抵扣金额',
  })
  couponDeduction: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '是否有追加服务',
  })
  hasAdditionalServices: boolean;

  @Column({
    type: DataType.DECIMAL(8, 2),
    defaultValue: 0.0,
    comment: '追加服务实付总价（所有状态的追加服务totalFee总和）',
  })
  additionalServiceAmount: number;

  @Column({
    type: DataType.DECIMAL(8, 2),
    defaultValue: 0.0,
    comment: '追加服务原总价',
  })
  additionalServiceOriginalPrice: number;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '追加服务是否全部完成',
  })
  additionalServicesCompleted: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
    comment: '是否有特殊情况说明',
  })
  hasSpecialNote: boolean;

  @Column({
    type: DataType.STRING(255),
    comment: '微信支付预支付ID',
  })
  prepay_id?: string;

  @BelongsTo(() => Customer, { onDelete: 'SET NULL' })
  customer: Customer;

  @BelongsTo(() => Employee, { onDelete: 'SET NULL' })
  employee: Employee;

  @BelongsTo(() => CustomerAddress, { onDelete: 'SET NULL' })
  customerAddress: CustomerAddress;

  @HasMany(() => OrderDetail, { onDelete: 'CASCADE' })
  orderDetails: OrderDetail[];

  @HasMany(() => ServiceChangeLog, { onDelete: 'CASCADE' })
  changeLogs: ServiceChangeLog[];

  @HasMany(() => Review, { onDelete: 'CASCADE' })
  reviews: Review[];

  @HasMany(() => Complaint, { onDelete: 'SET NULL' })
  complaints: Complaint[];

  @HasMany(() => OrderDiscountInfo, { onDelete: 'CASCADE' })
  discountInfos: OrderDiscountInfo[];

  @HasMany(() => ServicePhoto, { onDelete: 'CASCADE' })
  servicePhotos: ServicePhoto[];

  @HasMany(() => OrderSpecialNote, { onDelete: 'CASCADE' })
  specialNotes: OrderSpecialNote[];

  // /** 创建前生成订单编号， 不生效，需要抽时间看 */
  // @BeforeCreate
  // static async generateSn(instance: Order) {
  //   // 生成订单编号：当前时间戳 + 4位随机数
  //   const timestamp = Date.now().toString();
  //   const random = Math.floor(Math.random() * 10000)
  //     .toString()
  //     .padStart(4, '0');
  //   instance.sn = `${timestamp}${random}`;
  // }
}
