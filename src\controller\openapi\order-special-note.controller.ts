import {
  Controller,
  Post,
  Put,
  Get,
  Del,
  Body,
  Param,
  Inject,
} from '@midwayjs/core';
import { OrderSpecialNoteService } from '../../service/order-special-note.service';
import { CustomError } from '../../error/custom.error';

@Controller('/openapi/order-special-notes')
export class OpenapiOrderSpecialNoteController {
  @Inject()
  orderSpecialNoteService: OrderSpecialNoteService;

  @Post('/', { summary: '员工添加订单特殊情况说明' })
  async create(
    @Body()
    body: {
      orderId: number;
      employeeId: number;
      content: string;
      photos?: string[];
    }
  ) {
    const { orderId, employeeId, content, photos } = body;

    if (!orderId) {
      throw new CustomError('订单ID不能为空', 400);
    }
    if (!employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }
    if (!content || content.trim() === '') {
      throw new CustomError('特殊情况说明内容不能为空', 400);
    }
    if (content.length > 1000) {
      throw new CustomError('特殊情况说明内容不能超过1000字符', 400);
    }

    return await this.orderSpecialNoteService.createSpecialNote({
      orderId,
      employeeId,
      content: content.trim(),
      photos,
    });
  }

  @Put('/:orderId', { summary: '员工更新订单特殊情况说明' })
  async update(
    @Param('orderId') orderId: number,
    @Body()
    body: {
      employeeId: number;
      content?: string;
      photos?: string[];
    }
  ) {
    const { employeeId, content, photos } = body;

    if (!employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }

    if (content !== undefined) {
      if (content.trim() === '') {
        throw new CustomError('特殊情况说明内容不能为空', 400);
      }
      if (content.length > 1000) {
        throw new CustomError('特殊情况说明内容不能超过1000字符', 400);
      }
    }

    const updateData: any = {};
    if (content !== undefined) {
      updateData.content = content.trim();
    }
    if (photos !== undefined) {
      updateData.photos = photos;
    }

    return await this.orderSpecialNoteService.updateSpecialNote(
      orderId,
      employeeId,
      updateData
    );
  }

  @Get('/:orderId', { summary: '查询订单特殊情况说明' })
  async getByOrderId(@Param('orderId') orderId: number) {
    const specialNote = await this.orderSpecialNoteService.getSpecialNoteByOrderId(
      orderId
    );
    
    if (!specialNote) {
      throw new CustomError('该订单暂无特殊情况说明', 404);
    }

    return specialNote;
  }

  @Del('/:orderId', { summary: '员工删除订单特殊情况说明' })
  async delete(
    @Param('orderId') orderId: number,
    @Body('employeeId') employeeId: number
  ) {
    if (!employeeId) {
      throw new CustomError('员工ID不能为空', 400);
    }

    return await this.orderSpecialNoteService.deleteSpecialNote(
      orderId,
      employeeId
    );
  }
}
